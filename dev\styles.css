/* CSS Variables for Theme Colors */
:root {
    /* Dark Mode Colors (Default) */
    --bg-color: #000000;
    --text-color: #ffffff;
    --text-color-secondary: rgba(255, 255, 255, 0.7);
    --text-color-muted: rgba(156, 163, 175, 1); /* gray-400 */
    --card-bg: rgba(255, 255, 255, 0.05);
    --card-bg-hover: rgba(255, 255, 255, 0.1);
    --card-border: rgba(255, 255, 255, 0.1);
    --particle-color: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.4);
    --widget-bg: rgba(255, 255, 255, 0.1);
    --hover-glow: rgba(255, 255, 255, 0.7);

    /* Faster, smoother transition settings for iOS-style switch */
    --theme-transition-duration: 0.3s;
    --theme-transition-timing: cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Light Mode Colors */
body.light-mode {
    --bg-color: #ffffff;
    --text-color: #000000; /* Changed to pure black for all text */
    --text-color-secondary: #000000; /* Changed to pure black */
    --text-color-muted: #000000; /* Changed to pure black */
    --card-bg: rgba(24, 24, 24, 0.05); /* Updated to use #181818 as base */
    --card-bg-hover: rgba(24, 24, 24, 0.08); /* Updated to use #181818 as base */
    --card-border: rgba(24, 24, 24, 0.15); /* Updated to use #181818 as base */
    --particle-color: #000000; /* Darker particles for better visibility */
    --shadow-color: rgba(24, 24, 24, 0.15); /* Updated to use #181818 as base */
    --widget-bg: rgba(24, 24, 24, 0.08); /* Updated to use #181818 as base */
    --hover-glow: rgba(0, 0, 0, 0.4); /* Updated to match new text color */
}

/* Base styles */
body {
    zoom: 0.7;
    background-color: var(--bg-color);
    color: var(--text-color);
    min-height: 100vh;
    overflow-y: auto;
    transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
                color var(--theme-transition-duration) var(--theme-transition-timing);

    /* Disable text selection */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard */
}

/* Apply smooth transitions to all elements */
*, *::before, *::after {
    transition: background-color var(--theme-transition-duration) var(--theme-transition-timing),
                color var(--theme-transition-duration) var(--theme-transition-timing),
                border-color var(--theme-transition-duration) var(--theme-transition-timing),
                box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

/* Smooth theme transition overlay */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-color);
    z-index: 9998;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--theme-transition-duration) var(--theme-transition-timing);
    backdrop-filter: blur(0px);
}

body.theme-transitioning::after {
    opacity: 0.1; /* Even more subtle overlay */
    backdrop-filter: blur(3px); /* Lighter blur effect for faster transitions */
}

/* Theme-specific text colors */
.theme-text-muted {
    color: var(--text-color-muted);
    transition: color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure all text elements use the correct color in light mode */
body.light-mode h1,
body.light-mode h2,
body.light-mode h3,
body.light-mode h4,
body.light-mode h5,
body.light-mode h6,
body.light-mode p,
body.light-mode span,
body.light-mode div,
body.light-mode a,
body.light-mode li {
    color: #000000;
}

/* Preserve muted text color for elements with theme-text-muted class */
body.light-mode .theme-text-muted {
    color: #000000; /* Changed to pure black */
}

/* Particle Container Styling */
#tsparticles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1; /* Ensure particles are behind content */
}

/* SVG Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px; /* Position at top right corner */
    z-index: 1001;
    cursor: pointer;
    border: none;
    background: var(--widget-bg);
    backdrop-filter: blur(10px);
    padding: 12px;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: var(--text-color);
}

.theme-toggle:hover {
    transform: scale(1.1);
    background: var(--card-bg-hover);
    box-shadow: 0 0 15px var(--shadow-color);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* SVG styling with animations */
.theme-toggle__classic {
    width: 24px;
    height: 24px;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Default: show crescent moon, hide sun */
.theme-toggle__classic .icon-moon {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    transition: opacity 0.3s ease, transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center;
}
.theme-toggle__classic .icon-sun,
.theme-toggle__classic .icon-sun .icon-sun-rays path {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.3s ease, transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center;
}

/* Light mode: hide moon, show sun with rays */
body.light-mode .theme-toggle__classic {
    transform: rotate(180deg);
}
body.light-mode .theme-toggle__classic .icon-moon {
    opacity: 0;
    transform: scale(0.8);
}
body.light-mode .theme-toggle__classic .icon-sun {
    opacity: 1;
    transform: scale(1);
}
body.light-mode .theme-toggle__classic .icon-sun .icon-sun-rays path {
    opacity: 1;
    transform: scale(1);
    transition-delay: 0.2s;
}

/* Hover flourish */
.theme-toggle:hover .theme-toggle__classic {
    transform: rotate(15deg);
}
body.light-mode .theme-toggle:hover .theme-toggle__classic {
    transform: rotate(195deg);
}

/* Light mode adjustments */
body.light-mode .theme-toggle {
    color: var(--text-color);
}
body.light-mode .theme-toggle__classic {
    color: #000000;
}

/* Loading Screen - Always Dark Mode */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000000; /* Hard-coded black for loading screen */
    z-index: 9999;
    display: flex;
    flex-direction: column; /* Adjusted for vertical layout */
    justify-content: center;
    align-items: center;
    transition: opacity 0.5s ease; /* Only transition opacity */
}
.loading-footer {
    position: absolute;
    bottom: 20px;
    text-align: center;
    color: rgba(255, 255, 255, 0.7); /* Hard-coded white with opacity */
    font-size: 0.9rem;
    width: 100%;
}
#loading-bar-container { /* Added a container for better control */
   width: 18rem; /* Wider container */
   height: 0.75rem; /* Taller container */
   background-color: rgba(255, 255, 255, 0.05); /* Hard-coded for dark mode */
   border-radius: 9999px; /* rounded-full */
   overflow: hidden;
   box-shadow: 0 0 15px rgba(255, 255, 255, 0.1); /* Subtle glow */
}
#loading-bar {
    height: 100%;
    width: 0;
    background-color: #000000; /* Hard-coded white */
    transition: width 0.1s linear;
}
#loading-percentage {
    margin-top: 1rem; /* mt-4 */
    color: #000000; /* Hard-coded white */
    font-size: 1.5rem; /* Larger text */
    font-weight: 500; /* Medium weight */
}

/* Clock */
.clock {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--widget-bg);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    border-radius: 15px;
    color: var(--text-color);
    font-size: 1.2rem;
    z-index: 1000;
    display: flex;
    gap: 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Weather Widget & Toggle */
.weather-toggle {
    position: fixed;
    top: 80px; /* Position below the clock */
    left: 20px;
    z-index: 1001;
    transition: transform 0.3s ease, background-color 0.3s ease;
    background: var(--widget-bg);
    padding: 0.75rem; /* p-3 */
    border-radius: 9999px; /* rounded-full */
    cursor: pointer;
    color: var(--text-color);
}
.weather-toggle:hover {
    transform: scale(1.1);
    background: var(--card-bg-hover);
}

/* Spotify Toggle Button */
.spotify-toggle {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1001;
    transition: transform 0.3s ease;
    background: rgba(30, 215, 96, 0.2); /* Spotify green with transparency */
    padding: 0.75rem; /* p-3 */
    border-radius: 9999px; /* rounded-full */
    cursor: pointer;
    color: white; /* Ensure icon is visible */
    border: none; /* Remove default button border */
}
.spotify-toggle:hover {
    transform: scale(1.1);
    background: rgba(30, 215, 96, 0.3); /* Brighter on hover */
}

.weather-widget {
    position: fixed;
    top: 80px; /* Same top position as weather toggle */
    left: 80px; /* Position to the right of the weather toggle */
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    z-index: 1000;
    transform: scale(0.95); /* Start slightly smaller */
    opacity: 0; /* Start invisible */
    visibility: hidden; /* Start hidden */
    /* Updated transition for smoother fade and scale */
    transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0.3s,
                background-color 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    width: 250px;
    transform-origin: top left; /* Scale originates from the left corner */

    /* Dark mode styles (default) */
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Light mode styles */
body.light-mode .weather-widget {
    background: rgba(240, 240, 240, 0.9);
    color: #181818;
    border: 1px solid rgba(24, 24, 24, 0.1);
}

/* Spotify Widget */
.spotify-widget {
    position: fixed;
    bottom: 80px; /* Position above the toggle button */
    left: 20px;
    background: #1db954; /* Spotify green background */
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 0; /* Remove padding to eliminate gaps */
    color: white;
    z-index: 1000;
    transform: scale(0.95); /* Start slightly smaller */
    opacity: 0; /* Start invisible */
    visibility: hidden; /* Start hidden */
    transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0.3s;
    width: 275px; /* Wider for the playlist */
    transform-origin: bottom left; /* Scale originates from the corner */
    border: none; /* Remove border to prevent gaps */
    overflow: hidden; /* Ensure content respects border-radius */
}

/* Spotify Widget iframe styling */
.spotify-widget iframe {
    border-radius: 15px; /* Match parent border-radius */
    display: block; /* Remove any default spacing */
    width: 100%;
    height: 380px;
    border: none; /* Remove iframe border */
    background: #1db954; /* Spotify green background for loading state */
}
.weather-widget.active, .spotify-widget.active {
    transform: scale(1);
    opacity: 1; /* Become visible */
    visibility: visible; /* Become visible */
    /* Updated transition timing for entry */
    transition: transform 0.3s ease-out, opacity 0.3s ease-out, visibility 0s 0s;
}
.forecast-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
body.light-mode .forecast-item {
    border-bottom: 1px solid rgba(24, 24, 24, 0.1);
}
.forecast-item:last-child {
    border-bottom: none;
}

/* Profile Picture Border & Orbs */
 .profile-border {
     position: relative;
     width: 224px; /* w-56 */
     height: 224px; /* h-56 */
     margin-left: auto;
     margin-right: auto;
     margin-bottom: 1rem; /* mb-4 */
 }
.profile-border::before,
.profile-border::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.8), transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: rotate 4s linear infinite;
    z-index: 5; /* Ensure border is above image but below orbs if needed */
}
.profile-border::after {
    background: conic-gradient(from 180deg, transparent, rgba(255, 255, 255, 0.4), transparent, rgba(255, 255, 255, 0.8), transparent);
    animation-direction: reverse; /* Use animation-direction */
    filter: blur(5px);
}
@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Subtle border for profile in light mode */
body.light-mode .profile-border::before {
    background: conic-gradient(from 0deg, transparent, rgba(0, 0, 0, 0.3), transparent, rgba(0, 0, 0, 0.2), transparent);
    opacity: 0.5;
}

body.light-mode .profile-border::after {
    background: conic-gradient(from 180deg, transparent, rgba(0, 0, 0, 0.2), transparent, rgba(0, 0, 0, 0.3), transparent);
    filter: blur(5px);
    opacity: 0.5;
}
.profile-border img,
.profile-border video {
    position: relative; /* Needed for z-index stacking */
    z-index: 10;
    border-radius: 50%; /* rounded-full */
    width: 100%;
    height: 100%;
    object-fit: cover; /* object-cover */
    transition: border 0.3s ease, box-shadow 0.3s ease;
}

/* Video-specific styles */
.profile-border video {
    display: block; /* Remove bottom margin/spacing */
    background-color: transparent; /* Ensure transparency */
    pointer-events: none; /* Prevent interaction with video */
    -webkit-mask-image: -webkit-radial-gradient(white, black); /* Fix for Safari border-radius overflow */
    mask-image: radial-gradient(white, black); /* Standard property for other browsers */
    -webkit-backface-visibility: hidden; /* Prevent flickering in some browsers */
    backface-visibility: hidden;
    transform: translateZ(0); /* Force hardware acceleration */
}

/* No outline in light mode */
body.light-mode .profile-border img,
body.light-mode .profile-border video {
    border: none;
    box-shadow: none;
}

/* Project Card Effects */
.border-effect {
    animation: borderRotate 4s linear infinite;
    position: absolute;
    inset: 0;
    border-width: 2px; /* border-2 */
    border-color: transparent; /* border-transparent */
    /* background applied via Tailwind */
    z-index: 0; /* Behind content */
    border-radius: 0.5rem; /* Match parent rounded-lg */
}
@keyframes borderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.project-card {
    transition: transform 0.3s ease, background-color 0.3s ease;
    position: relative; /* Needed for absolute positioning of border-effect */
    overflow: hidden; /* Clip the rotating border */
    background-color: var(--card-bg);
    border-radius: 0.5rem; /* rounded-lg */
    padding: 1rem; /* p-4 */
    cursor: pointer;
}
.project-card:hover {
    transform: translateY(-5px);
    background-color: var(--card-bg-hover);
}
.project-card > * { /* Ensure content is above the border effect */
    position: relative;
    z-index: 1;
}
.project-card img {
    border-radius: 0.25rem; /* rounded (slightly smaller than card) */
    margin-bottom: 1rem; /* mb-4 */
}

/* Service Card Effects */
.service-card { /* Added class for easier targeting */
    background-color: var(--card-bg);
    padding: 1.5rem; /* p-6 */
    border-radius: 0.5rem; /* rounded-lg */
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease; /* transition-all duration-300 */
}
.service-card:hover {
    transform: scale(1.05); /* hover:scale-105 */
    background-color: var(--card-bg-hover);
}
.service-card-border { /* Renamed for clarity */
     position: absolute;
     inset: 0;
     border-width: 2px; /* border-2 */
     border-color: transparent; /* border-transparent */
     opacity: 0.2; /* opacity-20 */
     /* Background gradient applied via Tailwind */
     z-index: 0;
     border-radius: 0.5rem; /* Match parent */
}
.service-card > * { /* Ensure content is above the border effect */
     position: relative;
     z-index: 1;
}

/* Utility: Gradient Text/Animation (if needed elsewhere) */
@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* RemixIcon color in light/dark mode */
[class^="ri-"] {
    color: var(--text-color);
    transition: color 0.3s ease;
}

/* Ensure icons use the correct color in light mode */
body.light-mode [class^="ri-"] {
    color: #000000;
}

/* Contact Toggle Button */
.contact-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1001;
    transition: transform 0.3s ease, background-color 0.3s ease;
    background: var(--widget-bg);
    padding: 0.75rem; /* p-3 */
    border-radius: 9999px; /* rounded-full */
    cursor: pointer;
    color: var(--text-color); /* Ensure icon is visible */
    border: none; /* Remove default button border */
}
.contact-toggle:hover {
    transform: scale(1.1);
    background: var(--card-bg-hover);
}

/* Contact Form Overlay */
.contact-overlay {
    position: fixed;
    inset: 0; /* top, right, bottom, left = 0 */
    background: rgba(0, 0, 0, 0.8); /* Semi-transparent black background */
    backdrop-filter: blur(10px);
    z-index: 5000; /* High z-index to cover everything */
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden; /* Use visibility for better accessibility and performance */
    transform: scale(0.95);
    transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), visibility 0s 0.4s; /* Delay visibility transition */
}
.contact-overlay.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
    transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), visibility 0s 0s;
}

/* Contact Form Container */
.contact-form-container {
    padding: 2rem; /* p-8 */
    border-radius: 0.5rem; /* rounded-lg */
    width: 90%;
    max-width: 500px; /* Limit max width */
    position: relative;
    box-shadow: 0 10px 30px var(--shadow-color);
    transition: background-color 0.3s ease, box-shadow 0.3s ease, color 0.3s ease, border-color 0.3s ease;

    /* Dark mode styles (default) */
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Light mode styles for contact form */
body.light-mode .contact-form-container {
    background: rgba(240, 240, 240, 0.9);
    color: #181818;
    border: 1px solid rgba(24, 24, 24, 0.1);
}

/* Contact Form Close Button */
.contact-close-button {
    position: absolute;
    top: 1rem; /* p-4 */
    right: 1rem; /* p-4 */
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.5rem; /* ri-xl */
    transition: color 0.3s ease, transform 0.3s ease;

    /* Dark mode styles (default) */
    color: rgba(255, 255, 255, 0.7);
}

body.light-mode .contact-close-button {
    color: #000000;
}

.contact-close-button:hover {
    transform: rotate(90deg);
}

/* Dark mode hover */
.contact-close-button:hover {
    color: white;
}

/* Light mode hover */
body.light-mode .contact-close-button:hover {
    color: #000000;
}

/* Form Elements Styling (using Tailwind utilities where possible) */
.contact-form-container input[type="text"],
.contact-form-container input[type="email"],
.contact-form-container textarea {
    padding: 0.75rem 1rem; /* py-3 px-4 */
    border-radius: 0.375rem; /* rounded-md */
    width: 100%;
    margin-bottom: 1rem; /* mb-4 */
    transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease, color 0.3s ease;

    /* Dark mode styles (default) */
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
}

/* Light mode styles for form inputs */
body.light-mode .contact-form-container input[type="text"],
body.light-mode .contact-form-container input[type="email"],
body.light-mode .contact-form-container textarea {
    background-color: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(24, 24, 24, 0.1);
    color: #181818;
}

.contact-form-container input[type="text"]:focus,
.contact-form-container input[type="email"]:focus,
.contact-form-container textarea:focus {
    outline: none;
    border-color: rgba(74, 144, 226, 0.8); /* secondary color */
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3); /* Ring effect */
}

/* Dark mode focus */
.contact-form-container input[type="text"]:focus,
.contact-form-container input[type="email"]:focus,
.contact-form-container textarea:focus {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Light mode focus */
body.light-mode .contact-form-container input[type="text"]:focus,
body.light-mode .contact-form-container input[type="email"]:focus,
body.light-mode .contact-form-container textarea:focus {
    background-color: rgba(255, 255, 255, 0.9);
}

.contact-form-container textarea {
    min-height: 120px; /* Adjust as needed */
    resize: vertical;
}

.contact-form-container button[type="submit"] {
    background-color: #4A90E2; /* Primary color */
    color: white;
    padding: 0.75rem 1.5rem; /* py-3 px-6 */
    border-radius: 0.375rem; /* rounded-md */
    font-weight: 600; /* font-semibold */
    width: 100%;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.contact-form-container button[type="submit"]:hover {
    background-color: #3a7ac2; /* Darker shade */
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Special animation effects for the submit button */
.contact-form-container button[type="submit"]::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    transition: width 0.6s ease-out, height 0.6s ease-out;
}

/* Button animation classes */
.btn-pulse {
    animation: button-pulse 0.8s ease-out;
}

.btn-success {
    background-color: #4CD964 !important;
    box-shadow: 0 0 15px rgba(76, 217, 100, 0.5) !important;
}

.btn-success::before {
    background: rgba(76, 217, 100, 0.3);
}

.btn-error {
    background-color: #FF3B30 !important;
    box-shadow: 0 0 15px rgba(255, 59, 48, 0.5) !important;
}

.btn-error::before {
    background: rgba(255, 59, 48, 0.3);
}

/* Particle effects for the button */
.btn-particle {
    position: absolute;
    background: white;
    border-radius: 50%;
    pointer-events: none;
    opacity: 0;
    z-index: 2;
}

/* Keyframes for button animations */
@keyframes button-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(74, 144, 226, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
    }
}

@keyframes particle-fade {
    0% {
        transform: translate(0, 0);
        opacity: 1;
    }
    100% {
        transform: translate(var(--tx), var(--ty));
        opacity: 0;
    }
}

@keyframes button-success {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes button-error {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Placeholder text color - Dark mode (default) */
.contact-form-container ::placeholder {
    color: rgba(255, 255, 255, 0.5);
    opacity: 1; /* Firefox */
}

.contact-form-container :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

.contact-form-container ::-ms-input-placeholder { /* Microsoft Edge */
    color: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

/* Placeholder text color - Light mode */
body.light-mode .contact-form-container ::placeholder {
    color: rgba(0, 0, 0, 0.7);
    opacity: 1; /* Firefox */
}

body.light-mode .contact-form-container :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: rgba(0, 0, 0, 0.7);
    opacity: 1;
}

body.light-mode .contact-form-container ::-ms-input-placeholder { /* Microsoft Edge */
    color: rgba(0, 0, 0, 0.7);
    opacity: 1;
}

 /* Form result message */
 #form-result {
    margin-top: 1rem; /* mt-4 */
    min-height: 1.5rem; /* Ensure space is reserved */
    text-align: center;
 }

/* Map Section */
.map-section {
    position: relative;
    height: 300px;
    overflow: hidden;
}
.map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.map-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Map Section Styling */
.map-section h2 {
    color: var(--text-color);
    transition: color 0.3s ease;
}

/* Technology Icon Tooltips */
.tech-icon-container {
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.tech-tooltip {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--widget-bg);
    color: var(--text-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s, background-color 0.3s ease, color 0.3s ease;
    pointer-events: none;
    z-index: 10;
    backdrop-filter: blur(5px);
    border: 1px solid var(--card-border);
}
.tech-icon-container:hover .tech-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Tech icon hover effect */
.tech-icon {
    color: var(--text-color-muted);
    transition: color 0.3s ease, transform 0.3s ease, filter 0.3s ease;
}
.tech-icon:hover {
    color: var(--text-color);
    filter: drop-shadow(0 0 15px var(--hover-glow));
}
/* Ensure tech icons use black in light mode */
body.light-mode .tech-icon {
    color: #000000;
}

/* Map Container - Simple Clean Style */
.map-container {
  position: relative;
  overflow: hidden; /* Ensures content fits rounded corners */
  width: 100%;
  padding-top: 50%; /* Aspect Ratio (height/width * 100). Adjust 50% for desired height */
  border-radius: 0.5rem; /* rounded-lg */
  background-color: var(--card-bg); /* Dark background placeholder */
  box-shadow: 0 4px 12px var(--shadow-color); /* Subtle shadow */
  border: 1px solid var(--card-border); /* Subtle border */
  transition: background-color 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
}

/* Footer with Image and Copyright Text */
.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
  margin-top: 2rem;
  margin-bottom: 2rem;
  position: relative; /* For positioning the copyright text */
  padding-bottom: 5rem; /* Space for copyright text */
}

/* Copyright text that appears on hover */
.footer-copyright {
  position: absolute;
  bottom: 1rem; /* Position below the image */
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--widget-bg);
  color: var(--text-color);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

/* Ensure footer copyright text is black in light mode */
body.light-mode .footer-copyright {
  color: #000000;
}

/* Show copyright on footer hover */
.footer:hover .footer-copyright {
  opacity: 1;
}

.footer-image {
  max-width: 200px; /* Reduced size from 300px to 200px */
  width: 100%;
  height: auto; /* Maintain aspect ratio */
  object-fit: contain; /* Show full image */
  /* No border, shadow, transition, or other effects */
  /* Disable image context menu options but allow hover */
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
  /* Allow pointer events for hover but prevent right-click menu */
  cursor: pointer; /* Show pointer cursor on hover */
  margin-bottom: 5rem; /* Add space below the image for the copyright text */
  position: relative; /* For positioning */
  z-index: 5; /* Ensure image is above background but below copyright */
}

/* Theme-specific image visibility */
.light-mode-image {
  display: none; /* Hidden by default (dark mode is default) */
}

.dark-mode-image {
  display: block; /* Visible by default (dark mode is default) */
}

/* Show light mode image when light mode is active */
body.light-mode .light-mode-image {
  display: block;
}

/* Hide dark mode image when light mode is active */
body.light-mode .dark-mode-image {
  display: none;
}

/* Map Container Wrapper - Simple */
.map-container-wrapper {
  margin-bottom: 1rem;
  position: relative;
}
.map-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border: none; /* Remove default iframe border */
  /* Enhanced monochrome filter for black/white/gray aesthetic */
  filter: grayscale(100%) brightness(0.8) contrast(1.2) invert(92%);
}

/* Category Card Styles */
.category-card {
    background-color: var(--card-bg);
    padding: 2rem; /* p-8 */
    border-radius: 0.75rem; /* rounded-xl */
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid var(--card-border);
    text-align: center;
}

.category-card:hover {
    transform: translateY(-8px) scale(1.02);
    background-color: var(--card-bg-hover);
    box-shadow: 0 20px 40px var(--shadow-color), 0 0 30px rgba(74, 144, 226, 0.2);
}

.category-card:focus {
    outline: 2px solid rgba(74, 144, 226, 0.5);
    outline-offset: 2px;
}

.category-card-border {
    position: absolute;
    inset: 0;
    border-width: 2px;
    border-color: transparent;
    opacity: 0.3;
    z-index: 0;
    border-radius: 0.75rem;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-card-border {
    opacity: 0.6;
}

.category-card > * {
    position: relative;
    z-index: 1;
}

.category-icon {
    color: var(--text-color);
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    color: var(--text-color);
    filter: drop-shadow(0 0 15px var(--hover-glow));
    transform: scale(1.1);
}

/* Resource Card Styles */
.resource-card {
    background-color: var(--card-bg);
    padding: 1.5rem; /* p-6 */
    border-radius: 0.5rem; /* rounded-lg */
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--card-border);
    display: block;
    text-decoration: none;
    color: inherit;
}

.resource-card:hover {
    transform: translateY(-4px);
    background-color: var(--card-bg-hover);
    box-shadow: 0 10px 25px var(--shadow-color);
    text-decoration: none;
    color: inherit;
}

.resource-card-border {
    position: absolute;
    inset: 0;
    border-width: 2px;
    border-color: transparent;
    opacity: 0.2;
    z-index: 0;
    border-radius: 0.5rem;
}

.resource-card > * {
    position: relative;
    z-index: 1;
}

/* Fade transitions for category switching */
.fade-out {
    opacity: 0;
    transform: translateY(20px);
}

.fade-in {
    opacity: 1;
    transform: translateY(0);
}